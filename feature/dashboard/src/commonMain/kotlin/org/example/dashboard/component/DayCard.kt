package org.example.dashboard.component

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import kotlinx.datetime.LocalDate
import org.example.core.utils.DateUtils

/**
 * Individual day card component
 */
@Composable
 fun DayCard(
    day: DateUtils.DayItemCurrentWeek,
    isSelected: Boolean,
    onDaySelected: (LocalDate) -> Unit
) {
    Box(
        modifier = Modifier
            .size(width = 36.dp, height = 48.dp)
            .clip(RoundedCornerShape(12.dp))
            .background(
                when {
                    day.isToday -> Color(0xFFFFD66B)
                    isSelected -> Color(0xFFF3F4F6)
                    else -> Color.Transparent
                }
            )
            .then(
                if (isSelected) {
                    Modifier.border(
                        width = 2.dp,
                        color = Color(0xFF3B82F6),
                        shape = RoundedCornerShape(12.dp)
                    )
                } else {
                    Modifier
                }
            )
            .clickable { onDaySelected(day.localDate) },
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
            Text(
                text = day.day,
                fontSize = 10.sp,
                color = when {
                    day.isToday -> Color(0xFF111827)
                    isSelected -> Color(0xFF3B82F6)
                    else -> Color(0xFF374151)
                },
                fontWeight = FontWeight.Normal
            )
            Spacer(modifier = Modifier.height(2.dp))
            Text(
                text = day.date.toString(),
                fontSize = 14.sp,
                color = when {
                    day.isToday -> Color(0xFF111827)
                    isSelected -> Color(0xFF3B82F6)
                    else -> Color(0xFF374151)
                },
                fontWeight = when {
                    day.isToday -> FontWeight.SemiBold
                    isSelected -> FontWeight.Medium
                    else -> FontWeight.Medium
                }
            )
        }
    }
}