package org.example.dashboard.component

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import org.example.dashboard.BudgetProgress

/**
 * Progress bar component with enhanced visualization
 */
@Composable
fun BudgetProgressBar(
    progressDetails: BudgetProgress,
    label: String
) {
    Column {
        Text(
            text = label,
            fontSize = 10.sp,
            color = Color(0xFF6B7280),
            fontWeight = FontWeight.Normal,
            modifier = Modifier.padding(bottom = 4.dp)
        )

        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(8.dp)
                .background(Color(0xFFE5E7EB), RoundedCornerShape(6.dp))
        ) {
            // Base progress (up to 100%)
            val baseProgress = minOf(progressDetails.progress, 1f)
            Box(
                modifier = Modifier
                    .fillMaxHeight()
                    .fillMaxWidth(baseProgress)
                    .background(
                        when {
                            progressDetails.isExceeded -> Color(0xFFFBBF24)
                            progressDetails.isNearLimit -> Color(0xFFF59E0B)
                            else -> Color(0xFF10B981)
                        },
                        RoundedCornerShape(6.dp)
                    )
            )

            // Exceeded progress (over 100%) - red
            if (progressDetails.progress > 1f) {
                val exceededProgress = progressDetails.progress - 1f
                Box(
                    modifier = Modifier
                        .fillMaxHeight()
                        .fillMaxWidth(minOf(exceededProgress, 1f))
                        .background(Color(0xFFEF4444), RoundedCornerShape(6.dp))
                )
            }
        }
    }
}