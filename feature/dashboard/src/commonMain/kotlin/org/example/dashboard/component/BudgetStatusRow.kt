package org.example.dashboard.component

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import org.example.dashboard.BudgetProgress
import org.example.dashboard.formatPrice
import kotlin.math.abs

/**
 * Budget status row showing spent and remaining amounts
 */
@Composable
fun BudgetStatusRow(
    progressDetails: BudgetProgress,
    spent: Long,
    budget: Long,
    isAdaptiveBudgetActive: <PERSON><PERSON><PERSON>,
    isSelectedDateToday: <PERSON><PERSON><PERSON>,
    isShowingSpecificDay: Boolean
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        // Spent section
        Column(
            modifier = Modifier.padding(vertical = 16.dp)
        ) {
            Text(
                text = "Wydałeś",
                fontSize = 16.sp,
                fontWeight = FontWeight.Medium,
                color = Color(0xFF6B6B6B)
            )
            Text(
                text = formatPrice(spent),
                fontSize = 24.sp,
                fontWeight = FontWeight.Medium,
                color = when {
                    progressDetails.isExceeded -> Color(0xFFEF4444)
                    progressDetails.isNearLimit -> Color(0xFFF59E0B)
                    else -> Color(0xFF6B6B6B)
                }
            )

            // Additional info for adaptive budget
            if (isAdaptiveBudgetActive && isSelectedDateToday && isShowingSpecificDay) {
                AdaptiveBudgetInfo(spent = spent, budget = budget)
            }
        }

        // Remaining section
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = if (progressDetails.remainingBudget >= 0) "Zostało" else "Przekroczono o",
                fontSize = 16.sp,
                fontWeight = FontWeight.Medium,
                color = Color(0xFF6B6B6B)
            )
            Text(
                text = formatPrice(abs(progressDetails.remainingBudget)),
                fontSize = 24.sp,
                fontWeight = FontWeight.Medium,
                color = if (progressDetails.remainingBudget < 0) {
                    Color(0xFFEF4444)
                } else {
                    Color(0xFF10B981)
                }
            )

            // Percentage info
            AnimatedVisibility(
                visible = budget > 0,
                enter = fadeIn(),
                exit = fadeOut()
            ) {
                val percentage = (progressDetails.progress * 100).toInt()
                Text(
                    text = "${percentage}% budżetu",
                    fontSize = 10.sp,
                    color = Color(0xFF6B7280),
                    modifier = Modifier.padding(top = 2.dp)
                )
            }
        }
    }
}