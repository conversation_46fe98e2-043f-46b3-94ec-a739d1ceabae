package org.example.dashboard.component

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import org.example.core.utils.DateUtils
import org.example.dashboard.formatPrice
import kotlin.math.abs

/**
 * Adaptive budget info component showing variance from expected spending
 */
@Composable
fun AdaptiveBudgetInfo(spent: Long, budget: Long) {
    val timeOfDay = DateUtils.getCurrentTimeOfDay()
    val expectedByNow = (budget * timeOfDay).toLong()
    val difference = spent - expectedByNow

    AnimatedVisibility(
        visible = abs(difference) > (budget * 0.01),
        enter = fadeIn(),
        exit = fadeOut()
    ) {
        Text(
            text = when {
                difference > 0 -> "↗ ${formatPrice(difference)} ponad plan"
                difference < 0 -> "↘ ${formatPrice(abs(difference))} poniżej planu"
                else -> "🎯 Zgodnie z planem"
            },
            fontSize = 10.sp,
            color = when {
                difference > budget * 0.2 -> Color(0xFFEF4444)
                difference > 0 -> Color(0xFFF59E0B)
                else -> Color(0xFF10B981)
            },
            modifier = Modifier.padding(top = 2.dp)
        )
    }
}