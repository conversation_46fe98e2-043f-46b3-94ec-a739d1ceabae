package org.example.dashboard.component

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.scaleIn
import androidx.compose.animation.scaleOut
import androidx.compose.animation.slideInVertically
import androidx.compose.animation.slideOutVertically
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.rounded.CrisisAlert
import androidx.compose.material.icons.rounded.Lightbulb
import androidx.compose.material.icons.rounded.SelfImprovement
import androidx.compose.material3.Icon
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import kotlinx.datetime.LocalDate
import org.example.core.utils.DateUtils
import org.example.dashboard.BudgetProgress
import org.example.dashboard.BudgetType
import org.example.dashboard.SpendingRecommendation
import org.example.dashboard.formatPrice

/**
 * Composable function that displays the current week's budget information.
 * It shows the week number, daily budget for the selected day (or weekly if no day is selected),
 * a row of selectable days in the current week, progress bars indicating spending,
 * and details about the amount spent and remaining.
 * It also supports an adaptive budget feature, which can provide a "smart" daily budget
 * and spending recommendations.
 *
 * @param selectedDate The currently selected date. If null, the weekly budget is displayed.
 * @param onDaySelected Callback function invoked when a day is selected.
 * @param weeklyBudget The total weekly budget.
 * @param weeklySpent The amount spent in the week.
 * @param dailyBudget The daily budget for the selected day.
 * @param dailySpent The amount spent on the selected day.
 * @param adaptiveDailyBudget The calculated adaptive daily budget, used if `isAdaptiveBudgetActive` is true and a day is selected.
 * @param budgetProgressDetails Optional pre-calculated budget progress details. If null, they will be calculated internally.
 * @param spendingRecommendation Optional spending recommendation, displayed if `isAdaptiveBudgetActive` is true.
 * @param isAdaptiveBudgetActive Flag to enable/disable the adaptive budget feature. Defaults to true.
 * @param budgetType The type of budget being displayed (e.g., base, current adaptive, retrospective, predictive).
 */
@Composable
fun CurrentWeekBudget(
    selectedDate: LocalDate? = null,
    onDaySelected: (LocalDate) -> Unit = {},
    weeklyBudget: Long,
    weeklySpent: Long,
    dailyBudget: Long = 0L,
    dailySpent: Long = 0L,
    adaptiveDailyBudget: Long = 0L,
    budgetProgressDetails: BudgetProgress? = null,
    spendingRecommendation: SpendingRecommendation? = null,
    isAdaptiveBudgetActive: Boolean = true,
    budgetType: BudgetType = BudgetType.BASE,
) {
    val weekInfo by remember {
        mutableStateOf(DateUtils.getCurrentWeekInfoCurrentWeek())
    }

    // Local state for selected day to control daily progress bar visibility
    var internalSelectedDate by remember { mutableStateOf(selectedDate) }

    // Update internal state when external selectedDate changes
    if (internalSelectedDate != selectedDate) {
        internalSelectedDate = selectedDate
    }

    val today = DateUtils.timestampToLocalDate(DateUtils.getCurrentTimestamp())
    val isSelectedDateToday = internalSelectedDate == today
    val isShowingSpecificDay = internalSelectedDate != null

    // Calculate weekly progress (always shows weekly data)
    val weeklyProgressDetails = BudgetProgress(
        progress = if (weeklyBudget > 0) (weeklySpent.toFloat() / weeklyBudget.toFloat()).coerceIn(0f, 2f) else 0f,
        isExceeded = weeklySpent > weeklyBudget,
        isNearLimit = weeklySpent.toFloat() / weeklyBudget.toFloat() > 0.8f,
        remainingBudget = weeklyBudget - weeklySpent,
        usedBudget = weeklySpent,
        totalBudget = weeklyBudget
    )

    // Calculate daily progress (only when day is selected)
    val dailyProgressDetails = if (isShowingSpecificDay) {
        val effectiveDailyBudget = when {
            isSelectedDateToday && isAdaptiveBudgetActive -> adaptiveDailyBudget
            else -> dailyBudget
        }
        BudgetProgress(
            progress = if (effectiveDailyBudget > 0) (dailySpent.toFloat() / effectiveDailyBudget.toFloat()).coerceIn(0f, 2f) else 0f,
            isExceeded = dailySpent > effectiveDailyBudget,
            isNearLimit = dailySpent.toFloat() / effectiveDailyBudget.toFloat() > 0.8f,
            remainingBudget = effectiveDailyBudget - dailySpent,
            usedBudget = dailySpent,
            totalBudget = effectiveDailyBudget
        )
    } else null

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp)
    ) {
        // Header with week info and budget
        BudgetHeader(
            weekInfo = weekInfo,
            isShowingSpecificDay = isShowingSpecificDay,
            isAdaptiveBudgetActive = isAdaptiveBudgetActive,
            budgetType = budgetType,
            weeklyBudget = weeklyBudget,
            dailyBudget = dailyProgressDetails?.totalBudget ?: 0L,
            spendingRecommendation = spendingRecommendation
        )

        Spacer(modifier = Modifier.height(12.dp))

        // Days row
        WeekDaysRow(
            weekInfo = weekInfo,
            selectedDate = internalSelectedDate,
            onDaySelected = { date ->
                internalSelectedDate = if (internalSelectedDate == date) null else date
                onDaySelected(date)
            }
        )

        Spacer(modifier = Modifier.height(8.dp))

        // Weekly progress bar (always shows weekly progress)
        BudgetProgressBar(
            progressDetails = weeklyProgressDetails,
            label = "Progres tygodniowy"
        )

        // Daily progress bar (animated, shows only when day is selected)
        AnimatedVisibility(
            visible = isShowingSpecificDay && dailyProgressDetails != null,
            enter = slideInVertically(initialOffsetY = { it }) + fadeIn(),
            exit = slideOutVertically(targetOffsetY = { it }) + fadeOut()
        ) {
            dailyProgressDetails?.let { dailyProgress ->
                Column {
                    Spacer(modifier = Modifier.height(12.dp))
                    BudgetProgressBar(
                        progressDetails = dailyProgress,
                        label = "Progres dzienny"
                    )
                }
            }
        }

        Spacer(modifier = Modifier.height(8.dp))

        // Budget status row - shows data based on current context (weekly or daily)
        val currentProgressDetails = if (isShowingSpecificDay) dailyProgressDetails ?: weeklyProgressDetails else weeklyProgressDetails
        val currentSpent = if (isShowingSpecificDay) dailySpent else weeklySpent
        val currentBudget = currentProgressDetails.totalBudget

        BudgetStatusRow(
            progressDetails = currentProgressDetails,
            spent = currentSpent,
            budget = currentBudget,
            isAdaptiveBudgetActive = isAdaptiveBudgetActive,
            isSelectedDateToday = isSelectedDateToday,
            isShowingSpecificDay = isShowingSpecificDay
        )
    }
}

/**
 * Header component showing week number, budget info and spending recommendation
 */
@Composable
private fun BudgetHeader(
    weekInfo: DateUtils.CurrentWeekInfoCurrentWeek,
    isShowingSpecificDay: Boolean,
    isAdaptiveBudgetActive: Boolean,
    budgetType: BudgetType,
    weeklyBudget: Long,
    dailyBudget: Long,
    spendingRecommendation: SpendingRecommendation?
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        // Week number section
        Column(
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = "Tydzień",
                fontSize = 10.sp,
                color = Color(0xFF6B7280),
                fontWeight = FontWeight.Normal
            )
            Text(
                text = weekInfo.weekNumber.toString(),
                fontSize = 18.sp,
                color = Color(0xFF111827),
                fontWeight = FontWeight.Bold
            )
        }

        // Spending recommendation
        AnimatedVisibility(
            visible = isAdaptiveBudgetActive && spendingRecommendation != null,
            enter = fadeIn() + scaleIn(),
            exit = fadeOut() + scaleOut()
        ) {
            spendingRecommendation?.let { recommendation ->
                SpendingRecommendationChip(recommendation = recommendation)
            }
        }

        // Budget section with adaptive indicator
        BudgetSection(
            isShowingSpecificDay = isShowingSpecificDay,
            isAdaptiveBudgetActive = isAdaptiveBudgetActive,
            budgetType = budgetType,
            weeklyBudget = weeklyBudget,
            dailyBudget = dailyBudget
        )
    }
}

/**
 * Spending recommendation chip component
 */
@Composable
private fun SpendingRecommendationChip(recommendation: SpendingRecommendation) {
    Surface(
        modifier = Modifier.padding(top = 4.dp),
        shape = RoundedCornerShape(12.dp),
        color = when {
            recommendation.isOnTrack -> Color(0xFFECFDF5)
            recommendation.variance > 0 -> Color(0xFFFEF3C7)
            else -> Color(0xFFEFF6FF)
        }
    ) {
        Row(
            modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(4.dp)
        ) {
            Icon(
                imageVector = when {
                    recommendation.isOnTrack -> Icons.Rounded.SelfImprovement
                    recommendation.variance > 0 -> Icons.Rounded.CrisisAlert
                    else -> Icons.Rounded.Lightbulb
                },
                contentDescription = null,
                tint = when {
                    recommendation.isOnTrack -> Color(0xFF059669)
                    recommendation.variance > 0 -> Color(0xFFD97706)
                    else -> Color(0xFF2563EB)
                },
                modifier = Modifier.size(12.dp)
            )
            Text(
                text = when {
                    recommendation.isOnTrack -> "Na dobrej drodze"
                    recommendation.variance > 0 -> "Za szybko"
                    else -> "Dobrze idzie"
                },
                fontSize = 10.sp,
                fontWeight = FontWeight.Medium,
                color = when {
                    recommendation.isOnTrack -> Color(0xFF059669)
                    recommendation.variance > 0 -> Color(0xFFD97706)
                    else -> Color(0xFF2563EB)
                }
            )
        }
    }
}

/**
 * Budget section showing budget amount and type indicator
 */
@Composable
private fun BudgetSection(
    isShowingSpecificDay: Boolean,
    isAdaptiveBudgetActive: Boolean,
    budgetType: BudgetType,
    weeklyBudget: Long,
    dailyBudget: Long
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(4.dp)
        ) {
            Text(
                text = when {
                    isShowingSpecificDay && isAdaptiveBudgetActive -> {
                        when (budgetType) {
                            BudgetType.CURRENT_ADAPTIVE -> "Budżet (smart)"
                            BudgetType.RETROSPECTIVE -> "Budżet (retrospektywny)"
                            BudgetType.PREDICTIVE -> "Budżet (przewidywany)"
                            else -> "Budżet dzienny"
                        }
                    }
                    isShowingSpecificDay -> "Budżet dzienny"
                    else -> "Budżet tygodniowy"
                },
                fontSize = 10.sp,
                color = Color(0xFF6B7280),
                fontWeight = FontWeight.Normal
            )

            // Smart budget indicator
            if (isShowingSpecificDay && isAdaptiveBudgetActive) {
                val indicatorColor = when (budgetType) {
                    BudgetType.CURRENT_ADAPTIVE -> Color(0xFF10B981)
                    BudgetType.RETROSPECTIVE -> Color(0xFF6B7280)
                    BudgetType.PREDICTIVE -> Color(0xFF3B82F6)
                    else -> Color(0xFF10B981)
                }

                Box(
                    modifier = Modifier
                        .size(8.dp)
                        .background(indicatorColor, CircleShape)
                )
            }
        }

        Text(
            text = formatPrice(if (isShowingSpecificDay) dailyBudget else weeklyBudget),
            fontSize = 18.sp,
            color = Color(0xFF111827),
            fontWeight = FontWeight.Bold
        )
    }
}

/**
 * Week days row component for day selection
 */
@Composable
private fun WeekDaysRow(
    weekInfo: DateUtils.CurrentWeekInfoCurrentWeek,
    selectedDate: LocalDate?,
    onDaySelected: (LocalDate) -> Unit
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically
    ) {
        weekInfo.days.forEachIndexed { index, day ->
            val showDivider = index > 0 && day.month != weekInfo.days[index - 1].month

            Row(
                modifier = Modifier.weight(1f),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                // Month divider
                if (showDivider) {
                    Box(
                        modifier = Modifier
                            .width(1.dp)
                            .height(40.dp)
                            .background(Color(0xFFE5E5E5))
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                }

                // Day column
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.Center
                ) {
                    DayCard(
                        day = day,
                        isSelected = selectedDate == day.localDate,
                        onDaySelected = onDaySelected
                    )
                }
            }
        }
    }
}

/**
 * Individual day card component
 */
@Composable
private fun DayCard(
    day: DateUtils.DayItemCurrentWeek,
    isSelected: Boolean,
    onDaySelected: (LocalDate) -> Unit
) {
    Box(
        modifier = Modifier
            .size(width = 36.dp, height = 48.dp)
            .clip(RoundedCornerShape(12.dp))
            .background(
                when {
                    day.isToday -> Color(0xFFFFD66B)
                    isSelected -> Color(0xFFF3F4F6)
                    else -> Color.Transparent
                }
            )
            .then(
                if (isSelected) {
                    Modifier.border(
                        width = 2.dp,
                        color = Color(0xFF3B82F6),
                        shape = RoundedCornerShape(12.dp)
                    )
                } else {
                    Modifier
                }
            )
            .clickable { onDaySelected(day.localDate) },
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
            Text(
                text = day.day,
                fontSize = 10.sp,
                color = when {
                    day.isToday -> Color(0xFF111827)
                    isSelected -> Color(0xFF3B82F6)
                    else -> Color(0xFF374151)
                },
                fontWeight = FontWeight.Normal
            )
            Spacer(modifier = Modifier.height(2.dp))
            Text(
                text = day.date.toString(),
                fontSize = 14.sp,
                color = when {
                    day.isToday -> Color(0xFF111827)
                    isSelected -> Color(0xFF3B82F6)
                    else -> Color(0xFF374151)
                },
                fontWeight = when {
                    day.isToday -> FontWeight.SemiBold
                    isSelected -> FontWeight.Medium
                    else -> FontWeight.Medium
                }
            )
        }
    }
}

/**
 * Progress bar component with enhanced visualization
 */
@Composable
private fun BudgetProgressBar(
    progressDetails: BudgetProgress,
    label: String
) {
    Column {
        Text(
            text = label,
            fontSize = 10.sp,
            color = Color(0xFF6B7280),
            fontWeight = FontWeight.Normal,
            modifier = Modifier.padding(bottom = 4.dp)
        )

        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(8.dp)
                .background(Color(0xFFE5E7EB), RoundedCornerShape(6.dp))
        ) {
            // Base progress (up to 100%)
            val baseProgress = minOf(progressDetails.progress, 1f)
            Box(
                modifier = Modifier
                    .fillMaxHeight()
                    .fillMaxWidth(baseProgress)
                    .background(
                        when {
                            progressDetails.isExceeded -> Color(0xFFFBBF24)
                            progressDetails.isNearLimit -> Color(0xFFF59E0B)
                            else -> Color(0xFF10B981)
                        },
                        RoundedCornerShape(6.dp)
                    )
            )

            // Exceeded progress (over 100%) - red
            if (progressDetails.progress > 1f) {
                val exceededProgress = progressDetails.progress - 1f
                Box(
                    modifier = Modifier
                        .fillMaxHeight()
                        .fillMaxWidth(minOf(exceededProgress, 1f))
                        .background(Color(0xFFEF4444), RoundedCornerShape(6.dp))
                )
            }
        }
    }
}

/**
 * Budget status row showing spent and remaining amounts
 */
@Composable
private fun BudgetStatusRow(
    progressDetails: BudgetProgress,
    spent: Long,
    budget: Long,
    isAdaptiveBudgetActive: Boolean,
    isSelectedDateToday: Boolean,
    isShowingSpecificDay: Boolean
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        // Spent section
        Column(
            modifier = Modifier.padding(vertical = 16.dp)
        ) {
            Text(
                text = "Wydałeś",
                fontSize = 16.sp,
                fontWeight = FontWeight.Medium,
                color = Color(0xFF6B6B6B)
            )
            Text(
                text = formatPrice(spent),
                fontSize = 24.sp,
                fontWeight = FontWeight.Medium,
                color = when {
                    progressDetails.isExceeded -> Color(0xFFEF4444)
                    progressDetails.isNearLimit -> Color(0xFFF59E0B)
                    else -> Color(0xFF6B6B6B)
                }
            )

            // Additional info for adaptive budget
            if (isAdaptiveBudgetActive && isSelectedDateToday && isShowingSpecificDay) {
                AdaptiveBudgetInfo(spent = spent, budget = budget)
            }
        }

        // Remaining section
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = if (progressDetails.remainingBudget >= 0) "Zostało" else "Przekroczono o",
                fontSize = 16.sp,
                fontWeight = FontWeight.Medium,
                color = Color(0xFF6B6B6B)
            )
            Text(
                text = formatPrice(kotlin.math.abs(progressDetails.remainingBudget)),
                fontSize = 24.sp,
                fontWeight = FontWeight.Medium,
                color = if (progressDetails.remainingBudget < 0) {
                    Color(0xFFEF4444)
                } else {
                    Color(0xFF10B981)
                }
            )

            // Percentage info
            AnimatedVisibility(
                visible = budget > 0,
                enter = fadeIn(),
                exit = fadeOut()
            ) {
                val percentage = (progressDetails.progress * 100).toInt()
                Text(
                    text = "${percentage}% budżetu",
                    fontSize = 10.sp,
                    color = Color(0xFF6B7280),
                    modifier = Modifier.padding(top = 2.dp)
                )
            }
        }
    }
}

/**
 * Adaptive budget info component showing variance from expected spending
 */
@Composable
private fun AdaptiveBudgetInfo(spent: Long, budget: Long) {
    val timeOfDay = DateUtils.getCurrentTimeOfDay()
    val expectedByNow = (budget * timeOfDay).toLong()
    val difference = spent - expectedByNow

    AnimatedVisibility(
        visible = kotlin.math.abs(difference) > (budget * 0.01),
        enter = fadeIn(),
        exit = fadeOut()
    ) {
        Text(
            text = when {
                difference > 0 -> "↗ ${formatPrice(difference)} ponad plan"
                difference < 0 -> "↘ ${formatPrice(kotlin.math.abs(difference))} poniżej planu"
                else -> "🎯 Zgodnie z planem"
            },
            fontSize = 10.sp,
            color = when {
                difference > budget * 0.2 -> Color(0xFFEF4444)
                difference > 0 -> Color(0xFFF59E0B)
                else -> Color(0xFF10B981)
            },
            modifier = Modifier.padding(top = 2.dp)
        )
    }
}