package org.example.dashboard.component

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideInVertically
import androidx.compose.animation.slideOutVertically
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import kotlinx.datetime.LocalDate
import org.example.core.utils.DateUtils
import org.example.dashboard.BudgetProgress
import org.example.dashboard.BudgetType
import org.example.dashboard.SpendingRecommendation

/**
 * Composable function that displays the current week's budget information.
 * It shows the week number, daily budget for the selected day (or weekly if no day is selected),
 * a row of selectable days in the current week, progress bars indicating spending,
 * and details about the amount spent and remaining.
 * It also supports an adaptive budget feature, which can provide a "smart" daily budget
 * and spending recommendations.
 *
 * @param selectedDate The currently selected date. If null, the weekly budget is displayed.
 * @param onDaySelected Callback function invoked when a day is selected.
 * @param weeklyBudget The total weekly budget.
 * @param weeklySpent The amount spent in the week.
 * @param dailyBudget The daily budget for the selected day.
 * @param dailySpent The amount spent on the selected day.
 * @param adaptiveDailyBudget The calculated adaptive daily budget, used if `isAdaptiveBudgetActive` is true and a day is selected.
 * @param budgetProgressDetails Optional pre-calculated budget progress details. If null, they will be calculated internally.
 * @param spendingRecommendation Optional spending recommendation, displayed if `isAdaptiveBudgetActive` is true.
 * @param isAdaptiveBudgetActive Flag to enable/disable the adaptive budget feature. Defaults to true.
 * @param budgetType The type of budget being displayed (e.g., base, current adaptive, retrospective, predictive).
 */
@Composable
fun CurrentWeekBudget(
    selectedDate: LocalDate? = null,
    onDaySelected: (LocalDate) -> Unit = {},
    weeklyBudget: Long,
    weeklySpent: Long,
    dailyBudget: Long = 0L,
    dailySpent: Long = 0L,
    adaptiveDailyBudget: Long = 0L,
    budgetProgressDetails: BudgetProgress? = null,
    spendingRecommendation: SpendingRecommendation? = null,
    isAdaptiveBudgetActive: Boolean = true,
    budgetType: BudgetType = BudgetType.BASE,
) {
    val weekInfo by remember {
        mutableStateOf(DateUtils.getCurrentWeekInfoCurrentWeek())
    }

    // Local state for selected day to control daily progress bar visibility
    var internalSelectedDate by remember { mutableStateOf(selectedDate) }

    // Update internal state when external selectedDate changes
    if (internalSelectedDate != selectedDate) {
        internalSelectedDate = selectedDate
    }

    val today = DateUtils.timestampToLocalDate(DateUtils.getCurrentTimestamp())
    val isSelectedDateToday = internalSelectedDate == today
    val isShowingSpecificDay = internalSelectedDate != null

    // Calculate weekly progress (always shows weekly data)
    val weeklyProgressDetails = BudgetProgress(
        progress = if (weeklyBudget > 0) (weeklySpent.toFloat() / weeklyBudget.toFloat()).coerceIn(0f, 2f) else 0f,
        isExceeded = weeklySpent > weeklyBudget,
        isNearLimit = weeklySpent.toFloat() / weeklyBudget.toFloat() > 0.8f,
        remainingBudget = weeklyBudget - weeklySpent,
        usedBudget = weeklySpent,
        totalBudget = weeklyBudget
    )

    // Calculate daily progress (only when day is selected)
    val dailyProgressDetails = if (isShowingSpecificDay) {
        val effectiveDailyBudget = when {
            isSelectedDateToday && isAdaptiveBudgetActive -> adaptiveDailyBudget
            else -> dailyBudget
        }
        BudgetProgress(
            progress = if (effectiveDailyBudget > 0) (dailySpent.toFloat() / effectiveDailyBudget.toFloat()).coerceIn(0f, 2f) else 0f,
            isExceeded = dailySpent > effectiveDailyBudget,
            isNearLimit = dailySpent.toFloat() / effectiveDailyBudget.toFloat() > 0.8f,
            remainingBudget = effectiveDailyBudget - dailySpent,
            usedBudget = dailySpent,
            totalBudget = effectiveDailyBudget
        )
    } else null

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp)
    ) {
        // Header with week info and budget
        BudgetHeader(
            weekInfo = weekInfo,
            isShowingSpecificDay = isShowingSpecificDay,
            isAdaptiveBudgetActive = isAdaptiveBudgetActive,
            budgetType = budgetType,
            weeklyBudget = weeklyBudget,
            dailyBudget = dailyProgressDetails?.totalBudget ?: 0L,
            spendingRecommendation = spendingRecommendation
        )

        Spacer(modifier = Modifier.height(12.dp))

        // Days row
        WeekDaysRow(
            weekInfo = weekInfo,
            selectedDate = internalSelectedDate,
            onDaySelected = { date ->
                internalSelectedDate = if (internalSelectedDate == date) null else date
                onDaySelected(date)
            }
        )

        Spacer(modifier = Modifier.height(8.dp))

        // Weekly progress bar (always shows weekly progress)
        BudgetProgressBar(
            progressDetails = weeklyProgressDetails,
            label = "Progres tygodniowy"
        )

        // Daily progress bar (animated, shows only when day is selected)
        AnimatedVisibility(
            visible = isShowingSpecificDay && dailyProgressDetails != null,
            enter = slideInVertically(initialOffsetY = { it }) + fadeIn(),
            exit = slideOutVertically(targetOffsetY = { it }) + fadeOut()
        ) {
            dailyProgressDetails?.let { dailyProgress ->
                Column {
                    Spacer(modifier = Modifier.height(12.dp))
                    BudgetProgressBar(
                        progressDetails = dailyProgress,
                        label = "Progres dzienny"
                    )
                }
            }
        }

        Spacer(modifier = Modifier.height(8.dp))

        // Budget status row - shows data based on current context (weekly or daily)
        val currentProgressDetails = if (isShowingSpecificDay) dailyProgressDetails ?: weeklyProgressDetails else weeklyProgressDetails
        val currentSpent = if (isShowingSpecificDay) dailySpent else weeklySpent
        val currentBudget = currentProgressDetails.totalBudget

        BudgetStatusRow(
            progressDetails = currentProgressDetails,
            spent = currentSpent,
            budget = currentBudget,
            isAdaptiveBudgetActive = isAdaptiveBudgetActive,
            isSelectedDateToday = isSelectedDateToday,
            isShowingSpecificDay = isShowingSpecificDay
        )
    }
}