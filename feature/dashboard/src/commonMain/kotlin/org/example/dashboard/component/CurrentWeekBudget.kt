package org.example.dashboard.component

import androidx.compose.animation.AnimatedContent
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.scaleIn
import androidx.compose.animation.scaleOut
import androidx.compose.animation.slideInVertically
import androidx.compose.animation.slideOutVertically
import androidx.compose.animation.togetherWith
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.rounded.CrisisAlert
import androidx.compose.material.icons.rounded.Lightbulb
import androidx.compose.material.icons.rounded.SelfImprovement
import androidx.compose.material3.Icon
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import kotlinx.datetime.LocalDate
import org.example.core.utils.DateUtils
import org.example.dashboard.BudgetManager.DEFAULT_DAILY_BASE_BUDGET
import org.example.dashboard.BudgetProgress
import org.example.dashboard.BudgetType
import org.example.dashboard.SpendingRecommendation
import org.example.dashboard.formatPrice

/**
 * Composable function that displays the current week's budget information.
 * It shows the week number, daily budget for the selected day (or weekly if no day is selected),
 * a row of selectable days in the current week, a progress bar indicating spending,
 * and details about the amount spent and remaining.
 * It also supports an adaptive budget feature, which can provide a "smart" daily budget
 * and spending recommendations.
 *
 * @param selectedDate The currently selected date. If null, the weekly budget is displayed.
 * @param onDaySelected Callback function invoked when a day is selected.
 * @param budget The total budget for the period (week or day).
 * @param spent The amount spent in the period (week or day).
 * @param adaptiveDailyBudget The calculated adaptive daily budget, used if `isAdaptiveBudgetActive` is true and a day is selected.
 * @param budgetProgressDetails Optional pre-calculated budget progress details. If null, they will be calculated internally.
 * @param spendingRecommendation Optional spending recommendation, displayed if `isAdaptiveBudgetActive` is true.
 * @param isAdaptiveBudgetActive Flag to enable/disable the adaptive budget feature. Defaults to true.
 * @param budgetType The type of budget being displayed (e.g., base, current adaptive, retrospective, predictive).
 */
@Composable
fun CurrentWeekBudget(
    selectedDate: LocalDate? = null,
    onDaySelected: (LocalDate) -> Unit = {},
    budget: Long,
    spent: Long,
    // Nowe parametry dla adaptacyjnego budżetu
    adaptiveDailyBudget: Long = 0L,
    budgetProgressDetails: BudgetProgress? = null,
    spendingRecommendation: SpendingRecommendation? = null,
    isAdaptiveBudgetActive: Boolean = true, /*false*/
    budgetType: BudgetType = BudgetType.BASE,
) {
    val weekInfo by remember {
        mutableStateOf(DateUtils.getCurrentWeekInfoCurrentWeek())
    }

    // Sprawdź czy wybrany dzień to dzisiaj
    val today = DateUtils.timestampToLocalDate(DateUtils.getCurrentTimestamp())
    val isSelectedDateToday = selectedDate == today
    val isShowingSpecificDay = selectedDate != null

    // Wybierz odpowiednie wartości budżetu i wydatków na podstawie stanu
    val displayBudget = when {
        isShowingSpecificDay && isSelectedDateToday && isAdaptiveBudgetActive -> adaptiveDailyBudget
        isShowingSpecificDay -> budget // Użyj budżetu z uiState, nie kalkuluj ponownie
        else -> budget
    }

    val progressDetails = budgetProgressDetails ?: BudgetProgress(
        progress = if (displayBudget > 0) (spent.toFloat() / displayBudget.toFloat()).coerceIn(
            0f,
            2f
        ) else 0f,
        isExceeded = spent > displayBudget,
        isNearLimit = spent.toFloat() / displayBudget.toFloat() > 0.8f,
        remainingBudget = displayBudget - spent,
        usedBudget = spent,
        totalBudget = displayBudget
    )

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp)
    ) {
        // Header with week info and budget
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            // Week number section
            Column(
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = "Tydzień",
                    fontSize = 10.sp,
                    color = Color(0xFF6B7280),
                    fontWeight = FontWeight.Normal
                )
                Text(
                    text = weekInfo.weekNumber.toString(),
                    fontSize = 18.sp,
                    color = Color(0xFF111827),
                    fontWeight = FontWeight.Bold
                )
            }

            AnimatedVisibility(
                /*visible = isSelectedDateToday && isAdaptiveBudgetActive && spendingRecommendation != null,*/
                visible = isAdaptiveBudgetActive && spendingRecommendation != null,
                enter = fadeIn() + scaleIn(),
                exit = fadeOut() + scaleOut()
            ) {
                spendingRecommendation?.let { recommendation ->
                    Surface(
                        modifier = Modifier.padding(top = 4.dp),
                        shape = RoundedCornerShape(12.dp),
                        color = when {
                            recommendation.isOnTrack -> Color(0xFFECFDF5)
                            recommendation.variance > 0 -> Color(0xFFFEF3C7)
                            else -> Color(0xFFEFF6FF)
                        }
                    ) {
                        Row(
                            modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp),
                            verticalAlignment = Alignment.CenterVertically,
                            horizontalArrangement = Arrangement.spacedBy(4.dp)
                        ) {
                            Icon(
                                imageVector = when {
                                    recommendation.isOnTrack -> Icons.Rounded.SelfImprovement
                                    recommendation.variance > 0 -> Icons.Rounded.CrisisAlert
                                    else -> Icons.Rounded.Lightbulb
                                },
                                contentDescription = null,
                                tint = when {
                                    recommendation.isOnTrack -> Color(0xFF059669)
                                    recommendation.variance > 0 -> Color(0xFFD97706)
                                    else -> Color(0xFF2563EB)
                                },
                                modifier = Modifier.size(12.dp)
                            )
                            Text(
                                text = when {
                                    recommendation.isOnTrack -> "Na dobrej drodze"
                                    recommendation.variance > 0 -> "Za szybko"
                                    else -> "Dobrze idzie"
                                },
                                fontSize = 10.sp,
                                fontWeight = FontWeight.Medium,
                                color = when {
                                    recommendation.isOnTrack -> Color(0xFF059669)
                                    recommendation.variance > 0 -> Color(0xFFD97706)
                                    else -> Color(0xFF2563EB)
                                }
                            )
                        }
                    }
                }
            }

            // Budget section with adaptive indicator
            Column(
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(4.dp)
                ) {
                    Text(
                        text = when {
                            isShowingSpecificDay && isAdaptiveBudgetActive -> {
                                when (budgetType) {
                                    BudgetType.CURRENT_ADAPTIVE -> {
                                        println("CurrentWeekBudget: Current adaptive budget - isShowingSpecificDay:$isShowingSpecificDay && isSelectedDateToday:$isSelectedDateToday && isAdaptiveBudgetActive:$isAdaptiveBudgetActive")
                                        "Budżet (smart)"
                                    }

                                    BudgetType.RETROSPECTIVE -> {
                                        println("CurrentWeekBudget: Retrospective budget - budgetType:$budgetType")
                                        "Budżet (retrospektywny)"
                                    }

                                    BudgetType.PREDICTIVE -> {
                                        println("CurrentWeekBudget: Predictive budget - budgetType:$budgetType")
                                        "Budżet (przewidywany)"
                                    }

                                    else -> "Budżet dzienny"
                                }
                            }

                            isShowingSpecificDay -> {
                                println("CurrentWeekBudget: Base daily budget - isShowingSpecificDay:$isShowingSpecificDay && isAdaptiveBudgetActive:$isAdaptiveBudgetActive")
                                "Budżet dzienny"
                            }

                            else -> {
                                println("CurrentWeekBudget: Weekly budget - isShowingSpecificDay:$isShowingSpecificDay")
                                "Budżet"
                            }
                        },
                        fontSize = 10.sp,
                        color = Color(0xFF6B7280),
                        fontWeight = FontWeight.Normal
                    )

                    // Smart budget indicator
                    if (isShowingSpecificDay && isAdaptiveBudgetActive) {
                        val indicatorColor = when (budgetType) {
                            BudgetType.CURRENT_ADAPTIVE -> Color(0xFF10B981) // Zielony dla aktualnego adaptacyjnego
                            BudgetType.RETROSPECTIVE -> Color(0xFF6B7280) // Szary dla retrospektywnego
                            BudgetType.PREDICTIVE -> Color(0xFF3B82F6) // Niebieski dla przewidywanego
                            else -> Color(0xFF10B981)
                        }

                        Box(
                            modifier = Modifier
                                .size(8.dp)
                                .background(
                                    indicatorColor,
                                    CircleShape
                                )
                        )
                    }
                }
                println("CurrentWeekBudget: displayBudget=$displayBudget")
                Text(
                    text = formatPrice(displayBudget),
                    fontSize = 18.sp,
                    color = Color(0xFF111827),
                    fontWeight = FontWeight.Bold
                )
            }

        }

        Spacer(modifier = Modifier.height(12.dp))

        // Days row
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            weekInfo.days.forEachIndexed { index, day ->
                val showDivider = index > 0 &&
                        day.month != weekInfo.days[index - 1].month

                Row(
                    modifier = Modifier.weight(1f),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // Month divider
                    if (showDivider) {
                        Box(
                            modifier = Modifier
                                .width(1.dp)
                                .height(40.dp)
                                .background(Color(0xFFE5E5E5))
                        )
                        Spacer(modifier = Modifier.width(4.dp))
                    }

                    // Day column
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally,
                        verticalArrangement = Arrangement.Center
                    ) {
                        // Day card
                        val isSelected = selectedDate == day.localDate
                        Box(
                            modifier = Modifier
                                .size(width = 36.dp, height = 48.dp)
                                .clip(RoundedCornerShape(12.dp))
                                .background(
                                    when {
                                        day.isToday -> Color(0xFFFFD66B)
                                        isSelected -> Color(0xFFF3F4F6)
                                        else -> Color.Transparent
                                    }
                                )
                                .then(
                                    if (isSelected) {
                                        Modifier.border(
                                            width = 2.dp,
                                            color = Color(0xFF3B82F6),
                                            shape = RoundedCornerShape(12.dp)
                                        )
                                    } else {
                                        Modifier
                                    }
                                )
                                .clickable {
                                    onDaySelected(day.localDate)
                                },
                            contentAlignment = Alignment.Center
                        ) {
                            Column(
                                horizontalAlignment = Alignment.CenterHorizontally,
                                verticalArrangement = Arrangement.Center
                            ) {
                                Text(
                                    text = day.day,
                                    fontSize = 10.sp,
                                    color = when {
                                        day.isToday -> Color(0xFF111827)
                                        isSelected -> Color(0xFF3B82F6)
                                        else -> Color(0xFF374151)
                                    },
                                    fontWeight = FontWeight.Normal
                                )
                                Spacer(modifier = Modifier.height(2.dp))
                                Text(
                                    text = day.date.toString(),
                                    fontSize = 14.sp,
                                    color = when {
                                        day.isToday -> Color(0xFF111827)
                                        isSelected -> Color(0xFF3B82F6)
                                        else -> Color(0xFF374151)
                                    },
                                    fontWeight = when {
                                        day.isToday -> FontWeight.SemiBold
                                        isSelected -> FontWeight.Medium
                                        else -> FontWeight.Medium
                                    }
                                )
                            }
                        }
                    }
                }
            }
        }

        Spacer(modifier = Modifier.height(8.dp))


        // Progress bar with enhanced visualization
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(8.dp)
                .background(
                    Color(0xFFE5E7EB),
                    RoundedCornerShape(6.dp)
                )
        ) {
            // Base progress (up to 100%)
            val baseProgress = minOf(progressDetails.progress, 1f)
            Box(
                modifier = Modifier
                    .fillMaxHeight()
                    .fillMaxWidth(baseProgress)
                    .background(
                        when {
                            progressDetails.isExceeded -> Color(0xFFFBBF24) // Yellow for exceeded
                            progressDetails.isNearLimit -> Color(0xFFF59E0B) // Orange for near limit
                            else -> Color(0xFF10B981) // Green for normal
                        },
                        RoundedCornerShape(6.dp)
                    )
            )

            // Exceeded progress (over 100%) - red
            if (progressDetails.progress > 1f) {
                val exceededProgress = progressDetails.progress - 1f
                Box(
                    modifier = Modifier
                        .fillMaxHeight()
                        .fillMaxWidth(minOf(exceededProgress, 1f))
                        .background(
                            Color(0xFFEF4444), // Red for exceeded
                            RoundedCornerShape(6.dp)
                        )
                )
            }
        }

        Spacer(modifier = Modifier.height(8.dp))

        // Budget status row
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            Column(
                modifier = Modifier.padding(vertical = 16.dp)
            ) {
                Text(
                    text = "Wydałeś",
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium,
                    color = Color(0xFF6B6B6B)
                )
                println("CurrentWeekBudget: spent=$spent")
                Text(
                    text = formatPrice(spent),
                    fontSize = 24.sp,
                    fontWeight = FontWeight.Medium,
                    color = when {
                        progressDetails.isExceeded -> Color(0xFFEF4444)
                        progressDetails.isNearLimit -> Color(0xFFF59E0B)
                        else -> Color(0xFF6B6B6B)
                    }
                )

                // Additional info for adaptive budget
                if (/*isSelectedDateToday &&*/ isAdaptiveBudgetActive) {
                    val timeOfDay = DateUtils.getCurrentTimeOfDay()
                    val expectedByNow = (displayBudget * timeOfDay).toLong()
                    val difference = spent - expectedByNow

                    AnimatedVisibility(
                        visible = kotlin.math.abs(difference) > (displayBudget * 0.01/*0.1*/), // Show if >10% difference
                        enter = fadeIn(),
                        exit = fadeOut()
                    ) {
                        Text(
                            text = when {
                                difference > 0 -> "↗ ${formatPrice(difference)} ponad plan"
                                difference < 0 -> "↘ ${formatPrice(kotlin.math.abs(difference))} poniżej planu"
                                else -> "🎯 Zgodnie z planem"
                            },
                            fontSize = 10.sp,
                            color = when {
                                difference > displayBudget * 0.2 -> Color(0xFFEF4444)
                                difference > 0 -> Color(0xFFF59E0B)
                                else -> Color(0xFF10B981)
                            },
                            modifier = Modifier.padding(top = 2.dp)
                        )
                    }
                }
            }

            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Text(
                    text = if (progressDetails.remainingBudget >= 0) "Zostało" else "Przekroczono o",
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium,
                    color = Color(0xFF6B6B6B)
                )
                println("CurrentWeekBudget: progressDetails.remaining=${progressDetails.remainingBudget}")
                Text(
                    text = formatPrice(kotlin.math.abs(progressDetails.remainingBudget)),
                    fontSize = 24.sp,
                    fontWeight = FontWeight.Medium,
                    color = if (progressDetails.remainingBudget < 0) {
                        Color(0xFFEF4444)
                    } else {
                        Color(0xFF10B981)
                    }
                )

                // Percentage info
                AnimatedVisibility(
                    visible = displayBudget > 0,
                    enter = fadeIn(),
                    exit = fadeOut()
                ) {
                    val percentage = (progressDetails.progress * 100).toInt()
                    Text(
                        text = "${percentage}% budżetu",
                        fontSize = 10.sp,
                        color = Color(0xFF6B7280),
                        modifier = Modifier.padding(top = 2.dp)
                    )
                }
            }
        }
    }
}