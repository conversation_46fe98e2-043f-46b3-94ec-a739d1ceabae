package org.example.dashboard.component

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import org.example.dashboard.BudgetType
import org.example.dashboard.formatPrice

/**
 * Budget section showing budget amount and type indicator
 */
@Composable
fun BudgetSection(
    isShowingSpecificDay: Boolean,
    isAdaptiveBudgetActive: Boolean,
    budgetType: BudgetType,
    weeklyBudget: Long,
    dailyBudget: Long
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(4.dp)
        ) {
            Text(
                text = when {
                    isShowingSpecificDay && isAdaptiveBudgetActive -> {
                        when (budgetType) {
                            BudgetType.CURRENT_ADAPTIVE -> "Budżet (smart)"
                            BudgetType.RETROSPECTIVE -> "Budżet (retrospektywny)"
                            BudgetType.PREDICTIVE -> "Budżet (przewidywany)"
                            else -> "Budżet dzienny"
                        }
                    }

                    isShowingSpecificDay -> "Budżet dzienny"
                    else -> "Budżet tygodniowy"
                },
                fontSize = 10.sp,
                color = Color(0xFF6B7280),
                fontWeight = FontWeight.Normal
            )

            // Smart budget indicator
            if (isShowingSpecificDay && isAdaptiveBudgetActive) {
                val indicatorColor = when (budgetType) {
                    BudgetType.CURRENT_ADAPTIVE -> Color(0xFF10B981)
                    BudgetType.RETROSPECTIVE -> Color(0xFF6B7280)
                    BudgetType.PREDICTIVE -> Color(0xFF3B82F6)
                    else -> Color(0xFF10B981)
                }

                Box(
                    modifier = Modifier
                        .size(8.dp)
                        .background(indicatorColor, CircleShape)
                )
            }
        }

        Text(
            text = formatPrice(if (isShowingSpecificDay) dailyBudget else weeklyBudget),
            fontSize = 18.sp,
            color = Color(0xFF111827),
            fontWeight = FontWeight.Bold
        )
    }
}