package org.example.dashboard.component

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.rounded.CrisisAlert
import androidx.compose.material.icons.rounded.Lightbulb
import androidx.compose.material.icons.rounded.SelfImprovement
import androidx.compose.material3.Icon
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import org.example.dashboard.SpendingRecommendation

/**
 * Spending recommendation chip component
 */
@Composable
fun SpendingRecommendationChip(recommendation: SpendingRecommendation) {
    Surface(
        modifier = Modifier.padding(top = 4.dp),
        shape = RoundedCornerShape(12.dp),
        color = when {
            recommendation.isOnTrack -> Color(0xFFECFDF5)
            recommendation.variance > 0 -> Color(0xFFFEF3C7)
            else -> Color(0xFFEFF6FF)
        }
    ) {
        Row(
            modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(4.dp)
        ) {
            Icon(
                imageVector = when {
                    recommendation.isOnTrack -> Icons.Rounded.SelfImprovement
                    recommendation.variance > 0 -> Icons.Rounded.CrisisAlert
                    else -> Icons.Rounded.Lightbulb
                },
                contentDescription = null,
                tint = when {
                    recommendation.isOnTrack -> Color(0xFF059669)
                    recommendation.variance > 0 -> Color(0xFFD97706)
                    else -> Color(0xFF2563EB)
                },
                modifier = Modifier.size(12.dp)
            )
            Text(
                text = when {
                    recommendation.isOnTrack -> "Na dobrej drodze"
                    recommendation.variance > 0 -> "Za szybko"
                    else -> "Dobrze idzie"
                },
                fontSize = 10.sp,
                fontWeight = FontWeight.Medium,
                color = when {
                    recommendation.isOnTrack -> Color(0xFF059669)
                    recommendation.variance > 0 -> Color(0xFFD97706)
                    else -> Color(0xFF2563EB)
                }
            )
        }
    }
}