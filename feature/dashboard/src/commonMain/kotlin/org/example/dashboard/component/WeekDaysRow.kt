package org.example.dashboard.component

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.width
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import kotlinx.datetime.LocalDate
import org.example.core.utils.DateUtils

/**
 * Week days row component for day selection
 */
@Composable
fun WeekDaysRow(
    weekInfo: DateUtils.CurrentWeekInfoCurrentWeek,
    selectedDate: LocalDate?,
    onDaySelected: (LocalDate) -> Unit
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically
    ) {
        weekInfo.days.forEachIndexed { index, day ->
            val showDivider = index > 0 && day.month != weekInfo.days[index - 1].month

            Row(
                modifier = Modifier.weight(1f),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                // Month divider
                if (showDivider) {
                    Box(
                        modifier = Modifier
                            .width(1.dp)
                            .height(40.dp)
                            .background(Color(0xFFE5E5E5))
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                }

                // Day column
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.Center
                ) {
                    DayCard(
                        day = day,
                        isSelected = selectedDate == day.localDate,
                        onDaySelected = onDaySelected
                    )
                }
            }
        }
    }
}