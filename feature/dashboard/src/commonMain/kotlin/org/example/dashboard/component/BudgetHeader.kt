package org.example.dashboard.component

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.scaleIn
import androidx.compose.animation.scaleOut
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.sp
import org.example.core.utils.DateUtils
import org.example.dashboard.BudgetType
import org.example.dashboard.SpendingRecommendation

/**
 * Header component showing week number, budget info and spending recommendation
 */
@Composable
fun BudgetHeader(
    weekInfo: DateUtils.CurrentWeekInfoCurrentWeek,
    isShowingSpecificDay: <PERSON><PERSON>an,
    isAdaptiveBudgetActive: <PERSON>ole<PERSON>,
    budgetType: BudgetType,
    weeklyBudget: Long,
    dailyBudget: Long,
    spendingRecommendation: SpendingRecommendation?
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        // Week number section
        Column(
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = "Tydzień",
                fontSize = 10.sp,
                color = Color(0xFF6B7280),
                fontWeight = FontWeight.Normal
            )
            Text(
                text = weekInfo.weekNumber.toString(),
                fontSize = 18.sp,
                color = Color(0xFF111827),
                fontWeight = FontWeight.Bold
            )
        }

        // Spending recommendation
        AnimatedVisibility(
            visible = isAdaptiveBudgetActive && spendingRecommendation != null,
            enter = fadeIn() + scaleIn(),
            exit = fadeOut() + scaleOut()
        ) {
            spendingRecommendation?.let { recommendation ->
                SpendingRecommendationChip(recommendation = recommendation)
            }
        }

        // Budget section with adaptive indicator
        BudgetSection(
            isShowingSpecificDay = isShowingSpecificDay,
            isAdaptiveBudgetActive = isAdaptiveBudgetActive,
            budgetType = budgetType,
            weeklyBudget = weeklyBudget,
            dailyBudget = dailyBudget
        )
    }
}